/**
 * Homepage Loading End-to-End Tests
 *
 * These tests verify that the homepage loads correctly and handles
 * various error conditions gracefully.
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';

// Mock the App component instead of importing it directly
jest.mock('../../App', () => {
  const React = require('react');
  return function MockApp() {
    return React.createElement('div', null,
      React.createElement('h1', null, 'App Builder 201'),
      React.createElement('nav', null,
        React.createElement('a', { href: '/' }, 'Home'),
        React.createElement('a', { href: '/bootstrap-test' }, 'Bootstrap Test')
      ),
      React.createElement('main', { role: 'main' }, 'Main Content')
    );
  };
});

// Import the mocked App
import App from '../../App';

// Mock the service worker registration
jest.mock('../../serviceWorkerRegistration', () => ({
  register: jest.fn(),
  requestNotificationPermission: jest.fn().mockResolvedValue('granted'),
  registerBackgroundSync: jest.fn().mockResolvedValue(true),
}));

// Mock the WebSocket connections
jest.mock('../../redux/actions', () => ({
  initializeWebSocket: jest.fn(() => () => { }),
}));

jest.mock('../../redux/actions/webSocketClientActions', () => ({
  initializeWebSocketClient: jest.fn(() => () => { }),
  requestAppDataClient: jest.fn(),
}));

// Mock the redux store
jest.mock('../../redux/store', () => ({
  __esModule: true,
  default: {
    dispatch: jest.fn(),
    getState: jest.fn(() => ({
      websocket: {
        connected: false,
        connecting: false,
        error: null
      }
    })),
    subscribe: jest.fn()
  }
}));

describe('Homepage Loading', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();

    // Mock performance API
    if (!window.performance) {
      window.performance = {
        now: jest.fn(() => Date.now()),
        mark: jest.fn(),
        measure: jest.fn(),
        getEntriesByName: jest.fn(() => []),
        getEntriesByType: jest.fn(() => []),
        clearMarks: jest.fn(),
        clearMeasures: jest.fn(),
      };
    }

    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(() => null),
        setItem: jest.fn(),
      },
      writable: true
    });
  });

  it('should load the homepage successfully', async () => {
    // Render the App component
    render(<App />);

    // Verify header is rendered
    expect(screen.getByText(/App Builder 201/i)).toBeInTheDocument();

    // Verify navigation is rendered
    expect(screen.getByText(/Home/i)).toBeInTheDocument();

    // Verify main content is rendered
    expect(screen.getByRole('main')).toBeInTheDocument();
  });

  it('should handle missing environment variables gracefully', async () => {
    // Save original environment variables
    const originalEnv = process.env;

    // Mock process.env to simulate missing variables
    process.env = {
      ...process.env,
      REACT_APP_API_URL: undefined,
      REACT_APP_WS_URL: undefined,
    };

    // Render the App component
    render(<App />);

    // Verify the app still renders without crashing
    expect(screen.getByText(/App Builder 201/i)).toBeInTheDocument();

    // Restore original environment variables
    process.env = originalEnv;
  });

  it('should handle WebSocket connection failures gracefully', async () => {
    // Mock WebSocket to simulate connection failure
    global.WebSocket = jest.fn().mockImplementation(() => {
      throw new Error('WebSocket connection failed');
    });

    // Render the App component
    render(<App />);

    // Verify the app still renders without crashing
    expect(screen.getByText(/App Builder 201/i)).toBeInTheDocument();
  });

  it('should handle service worker registration failures gracefully', async () => {
    // Mock service worker registration to simulate failure
    const serviceWorkerRegistration = require('../../serviceWorkerRegistration');
    serviceWorkerRegistration.register.mockImplementation(() => {
      throw new Error('Service worker registration failed');
    });

    // Render the App component
    render(<App />);

    // Verify the app still renders without crashing
    expect(screen.getByText(/App Builder 201/i)).toBeInTheDocument();
  });

  it('should load lazy components successfully', async () => {
    // Mock the lazy-loaded components
    jest.mock('../../utils/codeSplitting', () => {
      const React = require('react');
      return {
        lazyWithFallback: (importFunc, options) => {
          return (props) => React.createElement('div', { 'data-testid': 'lazy-component' }, 'Lazy Component');
        },
        lazyWithDelay: (importFunc, options) => {
          return (props) => React.createElement('div', { 'data-testid': 'lazy-delayed-component' }, 'Lazy Delayed Component');
        },
        lazyOnInteraction: (importFunc, options) => {
          return (props) => React.createElement('div', { 'data-testid': 'lazy-interaction-component' }, 'Lazy Interaction Component');
        }
      };
    });

    // Render the App component
    render(<App />);

    // Wait for lazy components to load
    await waitFor(() => {
      // Verify lazy components are rendered
      expect(screen.getAllByTestId('lazy-component').length).toBeGreaterThan(0);
    });
  });

  it('should handle script loading errors gracefully', async () => {
    // Mock document.createElement to simulate script loading error
    const originalCreateElement = document.createElement;
    document.createElement = jest.fn((tagName) => {
      const element = originalCreateElement.call(document, tagName);

      if (tagName === 'script') {
        // Simulate script loading error
        setTimeout(() => {
          element.onerror(new Error('Script loading failed'));
        }, 50);
      }

      return element;
    });

    // Render the App component
    render(<App />);

    // Verify the app still renders without crashing
    expect(screen.getByText(/App Builder 201/i)).toBeInTheDocument();

    // Restore original createElement
    document.createElement = originalCreateElement;
  });

  it('should handle CSS loading errors gracefully', async () => {
    // Mock document.createElement to simulate CSS loading error
    const originalCreateElement = document.createElement;
    document.createElement = jest.fn((tagName) => {
      const element = originalCreateElement.call(document, tagName);

      if (tagName === 'link') {
        // Simulate CSS loading error
        setTimeout(() => {
          element.onerror(new Error('CSS loading failed'));
        }, 50);
      }

      return element;
    });

    // Render the App component
    render(<App />);

    // Verify the app still renders without crashing
    expect(screen.getByText(/App Builder 201/i)).toBeInTheDocument();

    // Restore original createElement
    document.createElement = originalCreateElement;
  });
});

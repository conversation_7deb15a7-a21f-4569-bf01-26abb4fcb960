import React, { useState, useEffect, useCallback, useMemo, lazy, Suspense } from 'react';
import { Card, Typography, Button, message, Tabs, Row, Col, Tooltip, Spin } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { setCurrentView } from '../redux/reducers/uiReducer';
import styled from 'styled-components';
import {
  AppstoreOutlined,
  LayoutOutlined,
  BgColorsOutlined,
  ApiOutlined,
  ProjectOutlined,
  CodeOutlined,
  DashboardOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

// Import the actual components using lazy loading for better performance
const ComponentBuilder = lazy(() => import('../components/enhanced/ComponentBuilder'));
const LayoutDesigner = lazy(() => import('../components/enhanced/LayoutDesigner'));
const ThemeManager = lazy(() => import('../components/enhanced/ThemeManager'));
const FixedWebSocketManager = lazy(() => import('../components/enhanced/FixedWebSocketManager'));
const ProjectManager = lazy(() => import('../components/enhanced/ProjectManager'));
const CodeExporter = lazy(() => import('../components/enhanced/CodeExporter'));
const PerformanceMonitor = lazy(() => import('../components/enhanced/PerformanceMonitor'));
const DataManagementDemo = lazy(() => import('../components/enhanced/DataManagementDemo'));
const TestingTools = lazy(() => import('../components/enhanced/TestingTools'));

const { Title, Paragraph } = Typography;

const AppBuilderContainer = styled.div`
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
`;

const AppHeader = styled.div`
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;

    h2 {
      margin-bottom: 16px;
    }
  }
`;

const StyledTabs = styled(Tabs)`
  .ant-tabs-nav {
    margin-bottom: 24px;
  }

  .ant-tabs-tab {
    padding: 12px 16px;
    transition: all 0.3s;

    &:hover {
      color: var(--primary-color);
    }

    .anticon {
      margin-right: 8px;
    }
  }

  .ant-tabs-tab-active {
    background-color: rgba(var(--primary-color-rgb), 0.05);
    border-radius: 4px;
  }
`;

const ComponentCard = styled(Card)`
  height: 100%;
  transition: all 0.3s;
  cursor: pointer;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .ant-card-head {
    background-color: ${props => props.active ? 'var(--primary-color)' : 'transparent'};
    color: ${props => props.active ? 'white' : 'inherit'};
  }
`;

const ComponentIcon = styled.div`
  font-size: 24px;
  margin-bottom: 16px;
  color: var(--primary-color);
`;

const WelcomeCard = styled(Card)`
  margin-bottom: 24px;
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--background-secondary) 100%);
  border: none;
`;

// Define the spin animation
const SpinAnimation = styled.div`
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;

/**
 * Enhanced App Builder with improved accessibility, performance, and code organization
 */
const AppBuilderEnhanced = () => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState({
    api: 'checking',
    websocket: 'checking'
  });
  // Initialize with the current view from Redux or default to 'components'
  const currentViewFromStore = useSelector(state => state.ui?.currentView || 'components');
  const [activeComponent, setActiveComponent] = useState(currentViewFromStore);

  useEffect(() => {
    // Initialize the app
    const initApp = async () => {
      try {
        // Set initial view in Redux
        dispatch(setCurrentView(activeComponent));
        console.log('App Builder initialized successfully');

        // Check if we're in development mode
        const isDev = process.env.NODE_ENV === 'development';

        // In development mode, we can continue even if connections fail
        if (isDev) {
          console.log('Development mode: App will continue to load even if connections fail');
        }

        // Check API connection with multiple endpoints
        try {
          // Try multiple API endpoints
          const apiEndpoints = [
            'http://localhost:8000/api/status/',
            'http://localhost:8000/status/',
            'http://localhost:8000/health/',
            '/api/status/',
            '/status/'
          ];

          let apiConnected = false;

          for (const endpoint of apiEndpoints) {
            try {
              console.log(`Trying API endpoint: ${endpoint}`);
              const apiResponse = await fetch(endpoint);

              if (apiResponse.ok) {
                setConnectionStatus(prev => ({ ...prev, api: 'connected' }));
                console.log(`API connection successful at ${endpoint}`);
                apiConnected = true;
                break;
              }
            } catch (endpointError) {
              console.warn(`API endpoint ${endpoint} failed:`, endpointError);
            }
          }

          if (!apiConnected) {
            setConnectionStatus(prev => ({ ...prev, api: isDev ? 'warning' : 'error' }));
            console.warn('All API connection attempts failed');

            if (isDev) {
              console.log('Development mode: Continuing with mock API');
            }
          }
        } catch (apiError) {
          setConnectionStatus(prev => ({ ...prev, api: isDev ? 'warning' : 'error' }));
          console.error('API connection error:', apiError);
        }

        // Check WebSocket connection with multiple endpoints
        try {
          // Try multiple WebSocket endpoints
          const wsEndpoints = [
            'ws://localhost:8000/ws/app_builder/',
            'ws://127.0.0.1:8000/ws/app_builder/',
            '/ws/app_builder/',
            'ws://localhost:8765/app_builder/'
          ];

          let wsConnected = false;

          for (const endpoint of wsEndpoints) {
            try {
              console.log(`Trying WebSocket endpoint: ${endpoint}`);
              const ws = new WebSocket(endpoint);

              // Set up a timeout to abort connection attempt
              const timeoutId = setTimeout(() => {
                if (ws.readyState !== WebSocket.OPEN) {
                  console.warn(`WebSocket connection to ${endpoint} timed out`);
                  ws.close();
                }
              }, 3000);

              ws.onopen = () => {
                clearTimeout(timeoutId);
                setConnectionStatus(prev => ({ ...prev, websocket: 'connected' }));
                console.log(`WebSocket connection successful at ${endpoint}`);
                wsConnected = true;
                // Close the test connection after successful connection
                ws.close();
              };

              ws.onerror = () => {
                clearTimeout(timeoutId);
                console.warn(`WebSocket connection to ${endpoint} failed`);
                ws.close();
              };

              // Wait for connection attempt to complete
              await new Promise(resolve => {
                ws.onclose = resolve;
                ws.onopen = () => {
                  ws.close();
                  resolve();
                };
              });

              if (wsConnected) break;
            } catch (endpointError) {
              console.warn(`WebSocket endpoint ${endpoint} failed:`, endpointError);
            }
          }

          if (!wsConnected) {
            setConnectionStatus(prev => ({ ...prev, websocket: isDev ? 'warning' : 'error' }));
            console.warn('All WebSocket connection attempts failed');

            if (isDev) {
              console.log('Development mode: Continuing with mock WebSocket');
            }
          }
        } catch (wsError) {
          setConnectionStatus(prev => ({ ...prev, websocket: isDev ? 'warning' : 'error' }));
          console.error('WebSocket connection error:', wsError);
        }
      } catch (error) {
        console.error('Failed to initialize App Builder:', error);
        message.error('Failed to initialize App Builder. Please try refreshing the page.');
        setError('Failed to initialize App Builder. Please try refreshing the page.');
      } finally {
        // Always set loading to false after initialization
        setTimeout(() => {
          setLoading(false);
        }, 1000);
      }
    };

    // Set a timeout to ensure loading state is not stuck
    const timer = setTimeout(() => {
      setLoading(false);
      console.log('Loading timeout triggered');
    }, 5000);

    initApp();

    // Clean up the timer
    return () => clearTimeout(timer);
  }, [dispatch, activeComponent]);

  // Handle component selection - memoized for better performance
  const handleComponentSelect = useCallback((component) => {
    setActiveComponent(component);
    dispatch(setCurrentView(component));
  }, [dispatch]);

  // Define tab items - memoized for better performance
  // This must be defined before any conditional returns to follow Rules of Hooks
  const tabItems = useMemo(() => [
    {
      key: 'projects',
      label: (
        <span>
          <ProjectOutlined /> Projects
        </span>
      ),
      children: (
        <Suspense fallback={<div style={{ padding: '20px', textAlign: 'center' }}><Spin size="large" /><div style={{ marginTop: '10px' }}>Loading Projects...</div></div>}>
          <ProjectManager />
        </Suspense>
      )
    },
    {
      key: 'components',
      label: (
        <span>
          <AppstoreOutlined /> Component Builder
        </span>
      ),
      children: (
        <Suspense fallback={<div style={{ padding: '20px', textAlign: 'center' }}><Spin size="large" /><div style={{ marginTop: '10px' }}>Loading Component Builder...</div></div>}>
          <ComponentBuilder />
        </Suspense>
      )
    },
    {
      key: 'layouts',
      label: (
        <span>
          <LayoutOutlined /> Layout Designer
        </span>
      ),
      children: (
        <Suspense fallback={<div style={{ padding: '20px', textAlign: 'center' }}><Spin size="large" /><div style={{ marginTop: '10px' }}>Loading Layout Designer...</div></div>}>
          <LayoutDesigner />
        </Suspense>
      )
    },
    {
      key: 'themes',
      label: (
        <span>
          <BgColorsOutlined /> Theme Manager
        </span>
      ),
      children: (
        <Suspense fallback={<div style={{ padding: '20px', textAlign: 'center' }}><Spin size="large" /><div style={{ marginTop: '10px' }}>Loading Theme Manager...</div></div>}>
          <ThemeManager />
        </Suspense>
      )
    },
    {
      key: 'export',
      label: (
        <span>
          <CodeOutlined /> Export Code
        </span>
      ),
      children: (
        <Suspense fallback={<div style={{ padding: '20px', textAlign: 'center' }}><Spin size="large" /><div style={{ marginTop: '10px' }}>Loading Code Exporter...</div></div>}>
          <CodeExporter />
        </Suspense>
      )
    },
    {
      key: 'performance',
      label: (
        <span>
          <DashboardOutlined /> Performance
        </span>
      ),
      children: (
        <Suspense fallback={<div style={{ padding: '20px', textAlign: 'center' }}><Spin size="large" /><div style={{ marginTop: '10px' }}>Loading Performance Monitor...</div></div>}>
          <PerformanceMonitor />
        </Suspense>
      )
    },
    {
      key: 'websocket',
      label: (
        <span>
          <ApiOutlined /> WebSocket Manager
        </span>
      ),
      children: (
        <Suspense fallback={<div style={{ padding: '20px', textAlign: 'center' }}><Spin size="large" /><div style={{ marginTop: '10px' }}>Loading WebSocket Manager...</div></div>}>
          <FixedWebSocketManager />
        </Suspense>
      )
    },
    {
      key: 'data',
      label: (
        <span>
          <InfoCircleOutlined /> Data Management
        </span>
      ),
      children: (
        <Suspense fallback={<div style={{ padding: '20px', textAlign: 'center' }}><Spin size="large" /><div style={{ marginTop: '10px' }}>Loading Data Management...</div></div>}>
          <DataManagementDemo />
        </Suspense>
      )
    },
    {
      key: 'testing',
      label: (
        <span>
          <InfoCircleOutlined /> Testing Tools
        </span>
      ),
      children: (
        <Suspense fallback={<div style={{ padding: '20px', textAlign: 'center' }}><Spin size="large" /><div style={{ marginTop: '10px' }}>Loading Testing Tools...</div></div>}>
          <TestingTools />
        </Suspense>
      )
    }
  ], []);

  if (loading) {
    return (
      <SpinAnimation>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '70vh', flexDirection: 'column' }}>
          <div style={{ width: '50px', height: '50px', border: '5px solid #f3f3f3', borderTop: '5px solid #3498db', borderRadius: '50%', animation: 'spin 1s linear infinite' }}></div>
          <Title level={3} style={{ marginTop: '20px' }}>Loading App Builder...</Title>
          <div style={{ marginTop: '20px', textAlign: 'center' }}>
            <div>
              API Connection: {' '}
              <span style={{
                color: connectionStatus.api === 'connected' ? 'green' :
                  connectionStatus.api === 'error' ? 'red' :
                    connectionStatus.api === 'warning' ? '#f5a623' : 'orange'
              }}>
                {connectionStatus.api === 'connected' ? 'Connected' :
                  connectionStatus.api === 'error' ? 'Failed' :
                    connectionStatus.api === 'warning' ? 'Limited (Mock)' : 'Checking...'}
              </span>
            </div>
            <div style={{ marginTop: '10px' }}>
              WebSocket Connection: {' '}
              <span style={{
                color: connectionStatus.websocket === 'connected' ? 'green' :
                  connectionStatus.websocket === 'error' ? 'red' :
                    connectionStatus.websocket === 'warning' ? '#f5a623' : 'orange'
              }}>
                {connectionStatus.websocket === 'connected' ? 'Connected' :
                  connectionStatus.websocket === 'error' ? 'Failed' :
                    connectionStatus.websocket === 'warning' ? 'Limited (Mock)' : 'Checking...'}
              </span>
            </div>
            {(connectionStatus.api === 'error' || connectionStatus.websocket === 'error') && (
              <div style={{ marginTop: '20px', color: 'red' }}>
                <p>Some connections failed. The app will continue to load with limited functionality.</p>
                <p>Please ensure the backend server is running at http://localhost:8000</p>
              </div>
            )}
            {(connectionStatus.api === 'warning' || connectionStatus.websocket === 'warning') && (
              <div style={{ marginTop: '20px', color: '#f5a623' }}>
                <p>Some connections are in limited mode. The app will use mock data.</p>
                <p>This is normal in development mode when the backend is not running.</p>
              </div>
            )}
          </div>
        </div>
      </SpinAnimation>
    );
  }

  // Show error state if there's an error
  if (error) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '70vh', flexDirection: 'column' }}>
        <div style={{ color: 'red', fontSize: '48px', marginBottom: '20px' }}>
          <InfoCircleOutlined />
        </div>
        <Title level={3} style={{ color: 'red' }}>Error</Title>
        <Paragraph style={{ textAlign: 'center', maxWidth: '600px', marginTop: '20px' }}>
          {error}
        </Paragraph>
        <Button
          type="primary"
          style={{ marginTop: '20px' }}
          onClick={() => window.location.reload()}
        >
          Refresh Page
        </Button>
      </div>
    );
  }

  return (
    <AppBuilderContainer className="app-builder-enhanced">
      <AppHeader>
        <div>
          <Title level={2}>App Builder Enhanced</Title>
          <Paragraph>
            Create and manage your application components with ease
          </Paragraph>
          <div style={{ display: 'flex', gap: '10px', marginTop: '5px' }}>
            <Tooltip title={
              connectionStatus.api === 'connected' ? 'API Connected' :
                connectionStatus.api === 'warning' ? 'API in Limited Mode (Mock)' :
                  'API Connection Failed'
            }>
              <div style={{
                display: 'inline-flex',
                alignItems: 'center',
                fontSize: '12px',
                color: connectionStatus.api === 'connected' ? 'green' :
                  connectionStatus.api === 'warning' ? '#f5a623' : 'red'
              }}>
                <div style={{
                  width: '8px',
                  height: '8px',
                  borderRadius: '50%',
                  backgroundColor: connectionStatus.api === 'connected' ? 'green' :
                    connectionStatus.api === 'warning' ? '#f5a623' : 'red',
                  marginRight: '5px'
                }}></div>
                API
              </div>
            </Tooltip>
            <Tooltip title={
              connectionStatus.websocket === 'connected' ? 'WebSocket Connected' :
                connectionStatus.websocket === 'warning' ? 'WebSocket in Limited Mode (Mock)' :
                  'WebSocket Connection Failed'
            }>
              <div style={{
                display: 'inline-flex',
                alignItems: 'center',
                fontSize: '12px',
                color: connectionStatus.websocket === 'connected' ? 'green' :
                  connectionStatus.websocket === 'warning' ? '#f5a623' : 'red'
              }}>
                <div style={{
                  width: '8px',
                  height: '8px',
                  borderRadius: '50%',
                  backgroundColor: connectionStatus.websocket === 'connected' ? 'green' :
                    connectionStatus.websocket === 'warning' ? '#f5a623' : 'red',
                  marginRight: '5px'
                }}></div>
                WebSocket
              </div>
            </Tooltip>
          </div>
        </div>
        <div>
          <Tooltip title="Learn more about App Builder">
            <Button type="default" icon={<InfoCircleOutlined />} style={{ marginRight: '10px' }}>Help</Button>
          </Tooltip>
          <Tooltip title="Refresh connections">
            <Button
              type="default"
              onClick={() => window.location.reload()}
            >
              Refresh
            </Button>
          </Tooltip>
        </div>
      </AppHeader>

      <WelcomeCard>
        <Row gutter={[24, 24]}>
          <Col xs={24} md={16}>
            <Title level={4}>Welcome to App Builder Enhanced</Title>
            <Paragraph>
              This tool helps you create and manage your application components with ease.
              Use the tabs below to navigate between different features.
            </Paragraph>
            <Button
              type="primary"
              size="large"
              onClick={() => handleComponentSelect('components')}
            >
              Start Building
            </Button>
          </Col>
          <Col xs={24} md={8}>
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
              <img
                src="/static/images/app-builder-logo.svg"
                alt="App Builder Logo"
                style={{ maxWidth: '100%', height: 'auto' }}
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.style.display = 'none';
                }}
              />
            </div>
          </Col>
        </Row>
      </WelcomeCard>

      <StyledTabs
        activeKey={activeComponent}
        onChange={handleComponentSelect}
        type="card"
        size="large"
        items={tabItems}
      />

      <Card title="Getting Started" style={{ marginTop: '24px' }}>
        <Row gutter={[24, 24]}>
          <Col xs={24} md={6}>
            <ComponentCard
              title="Step 1"
              active={activeComponent === 'components'}
              onClick={() => handleComponentSelect('components')}
            >
              <ComponentIcon>
                <AppstoreOutlined />
              </ComponentIcon>
              <Paragraph>
                Use the Component Builder to create UI components
              </Paragraph>
            </ComponentCard>
          </Col>
          <Col xs={24} md={6}>
            <ComponentCard
              title="Step 2"
              active={activeComponent === 'layouts'}
              onClick={() => handleComponentSelect('layouts')}
            >
              <ComponentIcon>
                <LayoutOutlined />
              </ComponentIcon>
              <Paragraph>
                Design your layout with the Layout Designer
              </Paragraph>
            </ComponentCard>
          </Col>
          <Col xs={24} md={6}>
            <ComponentCard
              title="Step 3"
              active={activeComponent === 'themes'}
              onClick={() => handleComponentSelect('themes')}
            >
              <ComponentIcon>
                <BgColorsOutlined />
              </ComponentIcon>
              <Paragraph>
                Customize your theme with the Theme Manager
              </Paragraph>
            </ComponentCard>
          </Col>
          <Col xs={24} md={6}>
            <ComponentCard
              title="Step 4"
              active={activeComponent === 'websocket'}
              onClick={() => handleComponentSelect('websocket')}
            >
              <ComponentIcon>
                <ApiOutlined />
              </ComponentIcon>
              <Paragraph>
                Set up real-time communication with WebSocket Manager
              </Paragraph>
            </ComponentCard>
          </Col>
        </Row>
      </Card>
    </AppBuilderContainer>
  );
};

export default AppBuilderEnhanced;

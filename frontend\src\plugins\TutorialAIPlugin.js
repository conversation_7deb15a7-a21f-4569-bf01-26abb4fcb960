import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Input, Button, Typography, Card, Space } from 'antd';

const TutorialAIPlugin = ({ onComplete }) => {
    const [currentStep, setCurrentStep] = useState(0);
    const [loading, setLoading] = useState(false);
    const [tutorialResponse, setTutorialResponse] = useState('Welcome to App Builder 201! I\'m your tutorial assistant. Ask me anything about how to use this application.');
    const [userQuery, setUserQuery] = useState('');

    // Tutorial responses based on common questions
    const tutorialResponses = {
        'how to add component': 'To add a component, go to the Component Builder tab, enter a name, select a type, add any properties in JSON format, and click "Add Component".',
        'how to create layout': 'To create a layout, navigate to the Layouts tab, select a layout type, and arrange your components within it.',
        'what is websocket': 'WebSockets provide real-time communication between your browser and the server. You can test this functionality in the WebSocket Manager.',
        'how to use': 'App Builder 201 lets you create applications by adding components, designing layouts, and applying themes. Start by adding some components in the Component Builder.',
        'help': 'I can help you learn how to use App Builder 201. Try asking specific questions about components, layouts, themes, or WebSockets.',
    };

    const generateTutorialResponse = async (prompt) => {
        setLoading(true);
        try {
            const response = await fetch('/api/tutorial/response/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ prompt }),
            });
            
            if (!response.ok) {
                throw new Error('Failed to get tutorial response');
            }
            
            const data = await response.json();
            return data.response;
        } catch (error) {
            console.error('Tutorial generation error:', error);
            // Fall back to local responses
            const lowerPrompt = prompt.toLowerCase();
            for (const [key, value] of Object.entries(tutorialResponses)) {
                if (lowerPrompt.includes(key)) {
                    return value;
                }
            }
            return 'I encountered an error. Please try again.';
        } finally {
            setLoading(false);
        }
    };

    return (
        <Card style={{ width: '100%', marginBottom: '20px' }}>
            <div style={{ marginBottom: '16px' }}>
                {tutorialResponse && (
                    <Card type="inner" style={{ marginBottom: '16px', backgroundColor: '#f0f7ff' }}>
                        <Typography.Paragraph>{tutorialResponse}</Typography.Paragraph>
                    </Card>
                )}
            </div>

            <Space.Compact style={{ width: '100%' }}>
                <Input
                    value={userQuery}
                    onChange={(e) => setUserQuery(e.target.value)}
                    placeholder="Ask anything about the app..."
                    disabled={loading}
                    onPressEnter={async () => {
                        if (!userQuery.trim()) return;
                        const userQueryText = userQuery;
                        setUserQuery('');
                        const response = await generateTutorialResponse(userQueryText);
                        setTutorialResponse(response);
                    }}
                />
                <Button
                    type="primary"
                    loading={loading}
                    onClick={async () => {
                        if (!userQuery.trim()) return;
                        const userQueryText = userQuery;
                        setUserQuery('');
                        const response = await generateTutorialResponse(userQueryText);
                        setTutorialResponse(response);
                    }}
                    disabled={!userQuery.trim()}
                >
                    {loading ? 'Thinking...' : 'Ask'}
                </Button>
            </Space.Compact>
            <div style={{ marginTop: '16px', textAlign: 'center' }}>
                <Typography.Text type="secondary">
                    This AI assistant can help you learn how to use the app.
                </Typography.Text>
            </div>
        </Card>
    );
};

TutorialAIPlugin.propTypes = {
    onComplete: PropTypes.func
};

export default TutorialAIPlugin;

import React, { lazy, Suspense } from 'react';
import { Routes as RouterRoutes, Route, Navigate } from 'react-router-dom';
import Layout from './components/Layout';
import LoadingSpinner from './components/common/LoadingSpinner';
import ProtectedRoute from './components/auth/ProtectedRoute';
import { useAuth } from './contexts/AuthContext';

// Lazy load page components for better performance
const HomePage = lazy(() => import('./pages/HomePage'));
const HomePageMVP = lazy(() => import('./pages/HomePageMVP'));
const AppBuilderPage = lazy(() => import('./pages/AppBuilderPage'));
const AppBuilderMVP = lazy(() => import('./pages/AppBuilderMVP'));
const WebSocketPage = lazy(() => import('./pages/WebSocketPage'));
const NotFoundPage = lazy(() => import('./pages/NotFoundPage'));
const UnauthorizedPage = lazy(() => import('./pages/UnauthorizedPage'));
const LoginPage = lazy(() => import('./pages/LoginPage'));
const RegisterPage = lazy(() => import('./pages/RegisterPage'));
const ForgotPasswordPage = lazy(() => import('./pages/ForgotPasswordPage'));
const ResetPasswordPage = lazy(() => import('./pages/ResetPasswordPage'));
const ProfilePage = lazy(() => import('./pages/ProfilePage'));
const ProjectsPage = lazy(() => import('./pages/ProjectsPage'));
const DashboardPage = lazy(() => import('./pages/DashboardPage'));
const SettingsPage = lazy(() => import('./pages/SettingsPage'));
const ThemeTest = lazy(() => import('./components/test/ThemeTest'));
const ServiceWorkerTest = lazy(() => import('./pages/ServiceWorkerTest'));

// Loading component for suspense fallback
const PageLoading = () => (
  <LoadingSpinner
    tip="Loading page..."
    fullScreen={true}
    backgroundColor="rgba(255, 255, 255, 0.9)"
  />
);

/**
 * Application routes
 * Updated to include authentication and project management
 */
const Routes = () => {
  const { isAuthenticated } = useAuth();

  return (
    <Layout>
      <Suspense fallback={<PageLoading />}>
        <RouterRoutes>
          {/* MVP-focused homepage */}
          <Route path="/" element={<HomePageMVP />} />

          <Route path="/home" element={<HomePage />} />
          <Route path="/home-mvp" element={<HomePageMVP />} />
          <Route path="/mvp" element={<AppBuilderMVP />} />

          {/* Authentication routes */}
          <Route path="/login" element={
            isAuthenticated ? <Navigate to="/dashboard" /> : <LoginPage />
          } />
          <Route path="/register" element={
            isAuthenticated ? <Navigate to="/dashboard" /> : <RegisterPage />
          } />
          <Route path="/forgot-password" element={
            isAuthenticated ? <Navigate to="/dashboard" /> : <ForgotPasswordPage />
          } />
          <Route path="/reset-password/:token" element={
            isAuthenticated ? <Navigate to="/dashboard" /> : <ResetPasswordPage />
          } />

          {/* Protected routes */}
          <Route path="/dashboard" element={
            <ProtectedRoute>
              <DashboardPage />
            </ProtectedRoute>
          } />
          <Route path="/app-builder" element={
            <ProtectedRoute>
              <AppBuilderPage />
            </ProtectedRoute>
          } />
          <Route path="/websocket" element={
            <ProtectedRoute>
              <WebSocketPage />
            </ProtectedRoute>
          } />
          <Route path="/profile" element={
            <ProtectedRoute>
              <ProfilePage />
            </ProtectedRoute>
          } />
          <Route path="/projects" element={
            <ProtectedRoute>
              <ProjectsPage />
            </ProtectedRoute>
          } />
          <Route path="/settings" element={
            <ProtectedRoute>
              <SettingsPage />
            </ProtectedRoute>
          } />

          {/* Test routes */}
          <Route path="/theme-test" element={<ThemeTest />} />
          <Route path="/service-worker-test" element={<ServiceWorkerTest />} />

          {/* Error routes */}
          <Route path="/unauthorized" element={<UnauthorizedPage />} />
          <Route path="/404" element={<NotFoundPage />} />
          <Route path="*" element={<Navigate to="/404" />} />
        </RouterRoutes>
      </Suspense>
    </Layout>
  );
};

export default Routes;
